
// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

void main() => runApp(const TechNewsApp());

class TechNewsApp extends StatelessWidget {
  const TechNewsApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Tech News',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        primaryColor: const Color(0xFF1E3A8A), // Tech news blue
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF1E3A8A),
          foregroundColor: Colors.white,
          elevation: 2,
        ),
      ),
      home: const TechNewsWebView(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class TechNewsWebView extends StatefulWidget {
  const TechNewsWebView({super.key});

  @override
  State<TechNewsWebView> createState() => _TechNewsWebViewState();
}

class _TechNewsWebViewState extends State<TechNewsWebView> {
  late final WebViewController controller;
  int loadingProgress = 0;
  bool isLoading = true;
  bool canGoBack = false;
  bool canGoForward = false;
  String? errorMessage;

  @override
  void initState() {
    super.initState();

    // Configure WebView for tech-news.io
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setUserAgent('Mozilla/5.0 (Linux; Android 10; Mobile) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36 TechNewsApp/1.0')
      ..enableZoom(true)
      ..setBackgroundColor(Colors.white)
      ..addJavaScriptChannel(
        'TechNewsApp',
        onMessageReceived: (JavaScriptMessage message) {
          // Handle messages from the web page if needed
        },
      )
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            setState(() {
              loadingProgress = progress;
            });
          },
          onPageStarted: (String url) {
            setState(() {
              isLoading = true;
              loadingProgress = 0;
              errorMessage = null; // Clear any previous errors
            });
          },
          onPageFinished: (String url) async {
            setState(() {
              isLoading = false;
              loadingProgress = 100;
            });
            // Update navigation state
            final canGoBackResult = await controller.canGoBack();
            final canGoForwardResult = await controller.canGoForward();
            setState(() {
              canGoBack = canGoBackResult;
              canGoForward = canGoForwardResult;
            });
          },
          onHttpError: (HttpResponseError error) {
            setState(() {
              errorMessage = 'HTTP Error ${error.response?.statusCode}: Failed to load content';
              isLoading = false;
            });
          },
          onWebResourceError: (WebResourceError error) {
            setState(() {
              errorMessage = 'Network Error: ${error.description}';
              isLoading = false;
            });
          },
          onNavigationRequest: (NavigationRequest request) {
            // Allow tech-news.io domain and necessary resources
            final uri = Uri.parse(request.url);
            final host = uri.host.toLowerCase();

            // Allow tech-news.io and its subdomains (primary)
            if (host == 'tech-news.io' ||
                host.endsWith('.tech-news.io') ||
                host == 'www.tech-news.io') {
              return NavigationDecision.navigate;
            }

            // Allow TechCrunch as fallback tech news site
            if (host == 'techcrunch.com' ||
                host.endsWith('.techcrunch.com') ||
                host == 'www.techcrunch.com') {
              return NavigationDecision.navigate;
            }

            // Allow common CDN and resource domains
            final allowedDomains = [
              'cdn.tech-news.io',
              'assets.tech-news.io',
              'static.tech-news.io',
              'images.tech-news.io',
              'api.tech-news.io',
              // TechCrunch resources
              'techcrunch.com',
              'wp.com',
              'gravatar.com',
              'wordpress.com',
              's0.wp.com',
              's1.wp.com',
              's2.wp.com',
              'secure.gravatar.com'
            ];

            if (allowedDomains.contains(host)) {
              return NavigationDecision.navigate;
            }

            // Block navigation to external domains
            return NavigationDecision.prevent;
          },
        ),
      )
      ..loadRequest(Uri.parse('https://techcrunch.com')); // Using a working tech news site as fallback
  }

  // #docregion webview_widget
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tech News'),
        centerTitle: true,
        bottom: loadingProgress < 100
            ? PreferredSize(
                preferredSize: const Size.fromHeight(4.0),
                child: LinearProgressIndicator(
                  value: loadingProgress / 100.0,
                  backgroundColor: Colors.grey[300],
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                ),
              )
            : null,
      ),
      body: Stack(
        children: [
          RefreshIndicator(
            onRefresh: () async {
              await controller.reload();
            },
            child: WebViewWidget(controller: controller),
          ),
          if (isLoading)
            Container(
              color: Colors.white.withValues(alpha: 0.8),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text(
                      'Loading TechCrunch...',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          if (errorMessage != null)
            Container(
              color: Colors.white.withValues(alpha: 0.95),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Connection Error',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32),
                      child: Text(
                        errorMessage!,
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontSize: 14),
                      ),
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: () async {
                        setState(() {
                          errorMessage = null;
                        });
                        await controller.reload();
                      },
                      icon: const Icon(Icons.refresh),
                      label: const Text('Try Again'),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
      bottomNavigationBar: Container(
        height: 60,
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: canGoBack
                  ? () async {
                      await controller.goBack();
                    }
                  : null,
              tooltip: 'Go Back',
            ),
            IconButton(
              icon: const Icon(Icons.arrow_forward),
              onPressed: canGoForward
                  ? () async {
                      await controller.goForward();
                    }
                  : null,
              tooltip: 'Go Forward',
            ),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () async {
                await controller.reload();
              },
              tooltip: 'Refresh',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () async {
                await controller.loadRequest(Uri.parse('https://techcrunch.com'));
              },
              tooltip: 'Home',
            ),
          ],
        ),
      ),
    );
  }
  // #enddocregion webview_widget
}
