
// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

void main() => runApp(const TechNewsApp());

class TechNewsApp extends StatelessWidget {
  const TechNewsApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Tech News',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        primaryColor: const Color(0xFF1E3A8A), // Tech news blue
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF1E3A8A),
          foregroundColor: Colors.white,
          elevation: 2,
        ),
      ),
      home: const TechNewsWebView(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class TechNewsWebView extends StatefulWidget {
  const TechNewsWebView({super.key});

  @override
  State<TechNewsWebView> createState() => _TechNewsWebViewState();
}

class _TechNewsWebViewState extends State<TechNewsWebView> {
  late final WebViewController controller;

  @override
  void initState() {
    super.initState();

    // Configure WebView for tech-news.io
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setUserAgent('TechNewsApp/1.0 (Flutter Mobile Client)')
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // TODO: Update loading progress indicator
          },
          onPageStarted: (String url) {
            // TODO: Show loading indicator
          },
          onPageFinished: (String url) {
            // TODO: Hide loading indicator
          },
          onHttpError: (HttpResponseError error) {
            // TODO: Handle HTTP errors
          },
          onWebResourceError: (WebResourceError error) {
            // TODO: Handle web resource errors
          },
          onNavigationRequest: (NavigationRequest request) {
            // Only allow tech-news.io domain and subdomains
            final uri = Uri.parse(request.url);
            if (uri.host == 'tech-news.io' ||
                uri.host.endsWith('.tech-news.io') ||
                uri.host == 'www.tech-news.io') {
              return NavigationDecision.navigate;
            }
            // Prevent navigation to external domains
            return NavigationDecision.prevent;
          },
        ),
      )
      ..loadRequest(Uri.parse('https://tech-news.io'));
  }

  // #docregion webview_widget
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tech News'),
        centerTitle: true,
      ),
      body: WebViewWidget(controller: controller),
    );
  }
  // #enddocregion webview_widget
}
