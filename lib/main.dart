
// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'dart:io';

void main() => runApp(const TechNewsApp());

class TechNewsApp extends StatelessWidget {
  const TechNewsApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Tech News',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        primaryColor: const Color(0xFF1E3A8A), // Tech news blue
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF1E3A8A),
          foregroundColor: Colors.white,
          elevation: 2,
        ),
      ),
      home: const TechNewsWebView(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class TechNewsWebView extends StatefulWidget {
  const TechNewsWebView({super.key});

  @override
  State<TechNewsWebView> createState() => _TechNewsWebViewState();
}

class _TechNewsWebViewState extends State<TechNewsWebView> {
  late final WebViewController controller;
  int loadingProgress = 0;
  bool isLoading = true;
  bool canGoBack = false;
  bool canGoForward = false;
  String? errorMessage;

  @override
  void initState() {
    super.initState();

    // Configure Android WebView settings to help with ORB issues
    if (Platform.isAndroid) {
      AndroidWebViewController.enableDebugging(true);
    }

    // Configure WebView specifically for your tech-news.io site
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setUserAgent('Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 TechNewsApp/1.0')
      ..enableZoom(true)
      ..setBackgroundColor(Colors.white)
      ..addJavaScriptChannel(
        'TechNewsApp',
        onMessageReceived: (JavaScriptMessage message) {
          // Handle messages from your tech-news.io site
          print('Message from tech-news.io: ${message.message}');
        },
      )
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            setState(() {
              loadingProgress = progress;
            });
          },
          onPageStarted: (String url) {
            setState(() {
              isLoading = true;
              loadingProgress = 0;
              errorMessage = null; // Clear any previous errors
            });
          },
          onPageFinished: (String url) async {
            setState(() {
              isLoading = false;
              loadingProgress = 100;
            });
            // Update navigation state
            final canGoBackResult = await controller.canGoBack();
            final canGoForwardResult = await controller.canGoForward();
            setState(() {
              canGoBack = canGoBackResult;
              canGoForward = canGoForwardResult;
            });
          },
          onHttpError: (HttpResponseError error) {
            setState(() {
              errorMessage = 'HTTP Error ${error.response?.statusCode}: Failed to load content';
              isLoading = false;
            });
          },
          onWebResourceError: (WebResourceError error) {
            setState(() {
              errorMessage = 'Network Error: ${error.description}';
              isLoading = false;
            });
          },
          onNavigationRequest: (NavigationRequest request) {
            // Allow tech news sites and necessary resources
            final uri = Uri.parse(request.url);
            final host = uri.host.toLowerCase();

            // Allow major tech news sites
            final allowedDomains = [
              // Primary tech-news.io domain
              'tech-news.io',
              'www.tech-news.io',
              'cdn.tech-news.io',
              'assets.tech-news.io',
              'static.tech-news.io',
              'images.tech-news.io',
              'api.tech-news.io',

              // Major tech news sites
              'techcrunch.com',
              'www.techcrunch.com',
              'arstechnica.com',
              'www.arstechnica.com',
              'theverge.com',
              'www.theverge.com',
              'wired.com',
              'www.wired.com',
              'engadget.com',
              'www.engadget.com',
              'gizmodo.com',
              'www.gizmodo.com',
              'mashable.com',
              'www.mashable.com',
              'venturebeat.com',
              'www.venturebeat.com',
              'recode.net',
              'www.recode.net',

              // Common CDN and resource domains
              'wp.com',
              'wordpress.com',
              'gravatar.com',
              'secure.gravatar.com',
              's0.wp.com',
              's1.wp.com',
              's2.wp.com',
              's3.wp.com',
              'i0.wp.com',
              'i1.wp.com',
              'i2.wp.com',
              'cdn.vox-cdn.com',
              'duet-cdn.vox-cdn.com',
              'chorus-cdn.vox-cdn.com',
              'cdn.arstechnica.net',
              'cdn.mos.cms.futurecdn.net',
              'images.unsplash.com',
              'cdn.pixabay.com',
              'media.giphy.com',

              // Social media embeds (common in tech articles)
              'twitter.com',
              'www.twitter.com',
              'youtube.com',
              'www.youtube.com',
              'youtu.be',
              'instagram.com',
              'www.instagram.com',

              // Analytics and tracking (needed for proper site function)
              'google-analytics.com',
              'googletagmanager.com',
              'doubleclick.net',
              'googlesyndication.com',
              'googleadservices.com',
              'facebook.com',
              'connect.facebook.net',

              // Common web fonts and libraries
              'fonts.googleapis.com',
              'fonts.gstatic.com',
              'ajax.googleapis.com',
              'cdnjs.cloudflare.com',
              'unpkg.com',
              'jsdelivr.net'
            ];

            // Check if the host is in allowed domains or is a subdomain of allowed domains
            for (final domain in allowedDomains) {
              if (host == domain || host.endsWith('.$domain')) {
                return NavigationDecision.navigate;
              }
            }

            // Block navigation to external domains
            // return NavigationDecision.prevent;
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(
        Uri.parse('https://tech-news.io'),
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate, br',
          'DNT': '1',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'none',
          'Cache-Control': 'max-age=0',
          'X-Requested-With': 'com.technews.app',
          'Origin': 'https://tech-news.io',
        },
      );
  }

  // #docregion webview_widget
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tech News'),
        centerTitle: true,
        bottom: loadingProgress < 100
            ? PreferredSize(
                preferredSize: const Size.fromHeight(4.0),
                child: LinearProgressIndicator(
                  value: loadingProgress / 100.0,
                  backgroundColor: Colors.grey[300],
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                ),
              )
            : null,
      ),
      body: Stack(
        children: [
          RefreshIndicator(
            onRefresh: () async {
              await controller.reload();
            },
            child: WebViewWidget(controller: controller),
          ),
          if (isLoading)
            Container(
              color: Colors.white.withValues(alpha: 0.8),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text(
                      'Loading Tech News...',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          if (errorMessage != null)
            Container(
              color: Colors.white.withValues(alpha: 0.95),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Connection Error',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32),
                      child: Text(
                        errorMessage!,
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontSize: 14),
                      ),
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: () async {
                        setState(() {
                          errorMessage = null;
                        });
                        await controller.reload();
                      },
                      icon: const Icon(Icons.refresh),
                      label: const Text('Try Again'),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
      bottomNavigationBar: Container(
        height: 60,
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: canGoBack
                  ? () async {
                      await controller.goBack();
                    }
                  : null,
              tooltip: 'Go Back',
            ),
            IconButton(
              icon: const Icon(Icons.arrow_forward),
              onPressed: canGoForward
                  ? () async {
                      await controller.goForward();
                    }
                  : null,
              tooltip: 'Go Forward',
            ),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () async {
                await controller.reload();
              },
              tooltip: 'Refresh',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () async {
                await controller.loadRequest(
                  Uri.parse('https://tech-news.io'),
                  headers: {
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Cache-Control': 'max-age=0',
                  },
                );
              },
              tooltip: 'Home',
            ),
          ],
        ),
      ),
    );
  }
  // #enddocregion webview_widget
}
